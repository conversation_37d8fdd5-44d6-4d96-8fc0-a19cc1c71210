<?php
/**
 * Create Upload Directories
 * MLM Binary Plan System
 * 
 * This script creates the necessary upload directories with proper permissions
 */

try {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>Create Upload Directories</title></head><body>\n";
    echo "<h1>📁 Creating Upload Directories</h1>\n";
    
    $directories = [
        'uploads/',
        'uploads/products/',
        'logs/'
    ];
    
    $results = [];
    
    foreach ($directories as $dir) {
        echo "<h3>Creating: {$dir}</h3>\n";
        
        if (is_dir($dir)) {
            echo "<p style='color: orange;'>⚠️ Directory already exists</p>\n";
            
            // Check if writable
            if (is_writable($dir)) {
                echo "<p style='color: green;'>✅ Directory is writable</p>\n";
                $results[$dir] = 'exists_writable';
            } else {
                echo "<p style='color: red;'>❌ Directory is not writable</p>\n";
                $results[$dir] = 'exists_not_writable';
            }
        } else {
            // Create directory
            if (mkdir($dir, 0755, true)) {
                echo "<p style='color: green;'>✅ Directory created successfully</p>\n";
                $results[$dir] = 'created';
            } else {
                echo "<p style='color: red;'>❌ Failed to create directory</p>\n";
                $results[$dir] = 'failed';
            }
        }
        
        // Set permissions if needed
        if (is_dir($dir)) {
            chmod($dir, 0755);
            echo "<p style='color: blue;'>ℹ️ Permissions set to 755</p>\n";
        }
    }
    
    // Create .htaccess for uploads/products/
    $htaccessPath = 'uploads/products/.htaccess';
    if (!file_exists($htaccessPath)) {
        $htaccessContent = "# Prevent direct access to uploaded files\n";
        $htaccessContent .= "Options -Indexes\n";
        $htaccessContent .= "# Allow only image files\n";
        $htaccessContent .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
        $htaccessContent .= "    Order allow,deny\n";
        $htaccessContent .= "    Allow from all\n";
        $htaccessContent .= "</FilesMatch>\n";
        $htaccessContent .= "# Deny all other files\n";
        $htaccessContent .= "<FilesMatch \"^(?!.*\\.(jpg|jpeg|png|gif|webp)$).*$\">\n";
        $htaccessContent .= "    Order deny,allow\n";
        $htaccessContent .= "    Deny from all\n";
        $htaccessContent .= "</FilesMatch>\n";
        
        if (file_put_contents($htaccessPath, $htaccessContent)) {
            echo "<h3>Creating: .htaccess security file</h3>\n";
            echo "<p style='color: green;'>✅ Security .htaccess file created</p>\n";
        }
    }
    
    // Test file creation
    echo "<h2>🧪 Testing File Operations</h2>\n";
    
    $testFile = 'uploads/products/test_' . uniqid() . '.txt';
    if (file_put_contents($testFile, 'test')) {
        echo "<p style='color: green;'>✅ File creation test successful</p>\n";
        unlink($testFile); // Clean up
        echo "<p style='color: green;'>✅ File deletion test successful</p>\n";
    } else {
        echo "<p style='color: red;'>❌ File creation test failed</p>\n";
    }
    
    echo "<h2>📋 Summary</h2>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr style='background: #f8f9fa;'><th>Directory</th><th>Status</th></tr>\n";
    
    foreach ($results as $dir => $status) {
        $color = 'black';
        $statusText = $status;
        
        switch ($status) {
            case 'created':
                $color = 'green';
                $statusText = '✅ Created';
                break;
            case 'exists_writable':
                $color = 'green';
                $statusText = '✅ Exists & Writable';
                break;
            case 'exists_not_writable':
                $color = 'red';
                $statusText = '❌ Exists but Not Writable';
                break;
            case 'failed':
                $color = 'red';
                $statusText = '❌ Creation Failed';
                break;
        }
        
        echo "<tr>\n";
        echo "<td>{$dir}</td>\n";
        echo "<td style='color: {$color};'>{$statusText}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ Upload Directories Setup Complete!</h3>\n";
    echo "<p>All necessary directories have been created with proper permissions.</p>\n";
    echo "<p>Your product image uploads should now work correctly.</p>\n";
    echo "</div>\n";
    
    echo "</body></html>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
