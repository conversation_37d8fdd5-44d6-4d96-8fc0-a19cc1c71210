Stack trace:
Frame         Function      Args
0007FFFFA900  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9800) msys-2.0.dll+0x1FE8E
0007FFFFA900  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABD8) msys-2.0.dll+0x67F9
0007FFFFA900  000210046832 (000210286019, 0007FFFFA7B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA900  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA900  000210068E24 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABE0  00021006A225 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA65BD0000 ntdll.dll
7FFA659D0000 KERNEL32.DLL
7FFA63270000 KERNELBASE.dll
7FFA63C20000 USER32.dll
7FFA62CC0000 win32u.dll
7FFA63DE0000 GDI32.dll
7FFA63020000 gdi32full.dll
7FFA63650000 msvcp_win.dll
7FFA63150000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA64E10000 advapi32.dll
7FFA63E10000 msvcrt.dll
7FFA638C0000 sechost.dll
7FFA637B0000 bcrypt.dll
7FFA64B00000 RPCRT4.dll
7FFA62330000 CRYPTBASE.DLL
7FFA637E0000 bcryptPrimitives.dll
7FFA65940000 IMM32.DLL
