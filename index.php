<?php
/**
 * Main Index Page
 * MLM Binary Plan System
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get featured products for homepage
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC LIMIT 6");
$productsStmt->execute();
$featuredProducts = $productsStmt->fetchAll();

$fileUpload = new FileUpload();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShaktiPure Industries - Gas Safety Solutions</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e40af;
            --secondary-blue: #3b82f6;
            --accent-orange: #f97316;
            --dark-blue: #1e293b;
            --light-blue: #dbeafe;
            --text-dark: #0f172a;
            --text-gray: #64748b;
            --white: #ffffff;
            --light-gray: #f8fafc;
            --border-gray: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.5;
            color: var(--text-dark);
            background-color: var(--white);
        }

        /* Top Banner */
        .top-banner {
            background: var(--primary-blue);
            color: white;
            text-align: center;
            padding: 8px 0;
            font-size: 13px;
            font-weight: 500;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border-gray);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            padding: 12px 0;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            font-size: 24px;
            font-weight: 800;
            color: var(--primary-blue);
        }

        .logo i {
            margin-right: 8px;
            font-size: 28px;
        }

        .search-container {
            flex: 1;
            max-width: 500px;
            margin: 0 40px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 2px solid var(--border-gray);
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-blue);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-action {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: var(--text-dark);
            font-size: 14px;
            font-weight: 500;
        }

        .header-action i {
            font-size: 18px;
        }

        .cart-badge {
            background: var(--accent-orange);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: -5px;
        }

        /* Navigation */
        .nav {
            background: var(--light-gray);
            padding: 12px 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .nav-menu a:hover {
            color: var(--primary-blue);
        }

        .nav-collections {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
        }

        .nav-collections a {
            color: var(--text-gray);
            text-decoration: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--dark-blue) 0%, var(--primary-blue) 100%);
            color: white;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="30" fill="rgba(255,255,255,0.1)"/></svg>') no-repeat center;
            background-size: 200px;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .hero-cta {
            display: flex;
            gap: 15px;
            margin-bottom: 40px;
        }

        .btn {
            padding: 14px 28px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--accent-orange);
            color: white;
        }

        .btn-primary:hover {
            background: #ea580c;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.1);
        }

        .hero-image {
            position: relative;
            text-align: center;
        }

        .hero-product {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: linear-gradient(135deg, var(--accent-orange), #dc2626);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        /* Slider Navigation */
        .hero-nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 40px;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .nav-dot.active {
            background: var(--accent-orange);
        }

        /* Collection Section */
        .collection {
            padding: 80px 0;
            background: var(--light-gray);
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-dark);
        }

        .section-subtitle {
            color: var(--text-gray);
            margin-top: 8px;
        }

        .view-all {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 600;
        }

        .collection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .collection-card {
            background: white;
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .collection-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .collection-icon {
            width: 80px;
            height: 80px;
            background: var(--light-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: var(--primary-blue);
        }

        .collection-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .collection-items {
            color: var(--text-gray);
            font-size: 14px;
        }

        /* Featured Products */
        .featured {
            padding: 80px 0;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
        }

        .product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .product-badge {
            position: absolute;
            top: 16px;
            left: 16px;
            background: var(--accent-orange);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 2;
        }

        .product-image {
            height: 240px;
            background: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-placeholder {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }

        .product-info {
            padding: 24px;
        }

        .product-brand {
            color: var(--text-gray);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .current-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        .original-price {
            font-size: 1rem;
            color: var(--text-gray);
            text-decoration: line-through;
        }

        .product-colors {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .color-option {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid var(--border-gray);
            cursor: pointer;
        }

        .product-actions {
            display: flex;
            gap: 12px;
        }

        .btn-add-cart {
            flex: 1;
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-add-cart:hover {
            background: var(--dark-blue);
        }

        .btn-quick-view {
            background: var(--light-gray);
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            color: var(--text-dark);
        }

        /* Deal Section */
        .deal {
            background: var(--dark-blue);
            color: white;
            padding: 80px 0;
        }

        .deal-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .deal-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .deal-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 16px;
        }

        .deal-subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.1rem;
        }

        .countdown {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }

        .countdown-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            min-width: 80px;
        }

        .countdown-number {
            font-size: 2rem;
            font-weight: 800;
            display: block;
        }

        .countdown-label {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.8);
        }

        .deal-products {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .deal-product {
            background: rgba(255,255,255,0.05);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }

        .deal-product-image {
            width: 80px;
            height: 80px;
            background: var(--primary-blue);
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        /* Category Banners */
        .category-banners {
            padding: 80px 0;
        }

        .banner-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .category-banner {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 20px;
            padding: 50px 40px;
            color: white;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .category-banner:hover {
            transform: scale(1.02);
        }

        .category-banner h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .category-banner p {
            color: rgba(255,255,255,0.9);
            margin-bottom: 24px;
        }

        /* Footer */
        .footer {
            background: var(--dark-blue);
            color: white;
            padding: 60px 0 20px;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: rgba(255,255,255,0.8);
        }

        .social-links {
            display: flex;
            gap: 15px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .social-link:hover {
            background: var(--accent-orange);
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-dark);
            cursor: pointer;
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 1000;
            padding: 20px;
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-gray);
        }

        .mobile-menu-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }

        .mobile-menu-nav {
            list-style: none;
        }

        .mobile-menu-nav li {
            margin-bottom: 20px;
        }

        .mobile-menu-nav a {
            color: var(--text-dark);
            text-decoration: none;
            font-size: 18px;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 40px;
            }

            .hero-content h1 {
                font-size: 2.8rem;
            }

            .collection-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 20px;
            }

            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .header-container {
                padding: 0 15px;
            }

            .search-container {
                margin: 0 20px;
            }

            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                padding: 60px 0;
            }

            .hero-content h1 {
                font-size: 2.2rem;
            }

            .hero-cta {
                flex-direction: column;
                align-items: center;
            }

            .section-title {
                font-size: 2rem;
            }

            .collection-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }

            .banner-grid {
                grid-template-columns: 1fr;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .header-container {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .search-container {
                margin: 0;
                max-width: 100%;
            }

            .header-actions {
                order: -1;
                width: 100%;
                justify-content: space-between;
            }

            .hero-content h1 {
                font-size: 1.8rem;
            }

            .section-container {
                padding: 0 15px;
            }

            .collection-grid {
                grid-template-columns: 1fr;
            }

            .countdown {
                gap: 10px;
            }

            .countdown-item {
                padding: 15px 10px;
                min-width: 60px;
            }

            .countdown-number {
                font-size: 1.5rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Scroll animations */
        .scroll-animate {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .scroll-animate.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Top Banner -->
    <div class="top-banner">
        Free Shipping Worldwide When Order Above ₹500 | Gas Safety Solutions
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-top">
            <div class="header-container">
                <a href="#" class="logo">
                    <i class="fas fa-shield-alt"></i>
                    ShaktiPure
                </a>

                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search products...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="header-actions">
                    <a href="#" class="header-action">
                        <i class="fas fa-user"></i>
                        <span>Account</span>
                    </a>
                    <a href="#" class="header-action">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Cart</span>
                        <span class="cart-badge">0</span>
                    </a>
                    <button class="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <nav class="nav">
            <div class="nav-container">
                <ul class="nav-menu">
                    <li><a href="#">Shop All</a></li>
                    <li><a href="#">Gas Safety</a></li>
                    <li><a href="#">Smart Devices</a></li>
                    <li><a href="#">Industrial</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>

                <div class="nav-collections">
                    <i class="fas fa-fire" style="color: var(--accent-orange);"></i>
                    <a href="#">All Collections</a>
                    <span>|</span>
                    <a href="#">About Us</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-header">
            <a href="#" class="logo">
                <i class="fas fa-shield-alt"></i>
                ShaktiPure
            </a>
            <button class="mobile-menu-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <ul class="mobile-menu-nav">
            <li><a href="#">Shop All</a></li>
            <li><a href="#">Gas Safety</a></li>
            <li><a href="#">Smart Devices</a></li>
            <li><a href="#">Industrial</a></li>
            <li><a href="#">About</a></li>
            <li><a href="#">Contact</a></li>
            <li><a href="#">Account</a></li>
        </ul>
    </div>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Stay Ahead With Latest Gas Safety Solutions</h1>
                <p class="hero-subtitle">Discover the newest safety devices and innovative systems that keep you ahead.</p>
                
                <div class="hero-cta">
                    <a href="#" class="btn btn-primary">
                        Buy Now <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="#" class="btn btn-secondary">
                        See More <i class="fas fa-play"></i>
                    </a>
                </div>
            </div>

            <div class="hero-image">
                <div class="hero-product">
                    <i class="fas fa-shield-alt"></i>
                </div>
            </div>
        </div>

        <div class="hero-nav">
            <span class="nav-dot active"></span>
            <span class="nav-dot"></span>
            <span class="nav-dot"></span>
        </div>
    </section>

    <!-- Collection Section -->
    <section class="collection">
        <div class="section-container">
            <div class="section-header">
                <div>
                    <h2 class="section-title">Collection</h2>
                    <p class="section-subtitle">Top 10 Most Sold This Week, Next Day Delivery</p>
                </div>
                <a href="#" class="view-all">
                    View all collections <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <div class="collection-grid">
                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Safety Devices</h3>
                    <p class="collection-items">8 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <h3>Control Systems</h3>
                    <p class="collection-items">2 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Smart Monitors</h3>
                    <p class="collection-items">6 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-print"></i>
                    </div>
                    <h3>Documentation</h3>
                    <p class="collection-items">3 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <h3>Alert Systems</h3>
                    <p class="collection-items">4 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <h3>Monitoring Hub</h3>
                    <p class="collection-items">5 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <h3>Data Storage</h3>
                    <p class="collection-items">4 items</p>
                </div>

                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-tablet-alt"></i>
                    </div>
                    <h3>Control Panels</h3>
                    <p class="collection-items">5 items</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Collection -->
    <section class="featured">
        <div class="section-container">
            <div class="section-header">
                <div>
                    <h2 class="section-title">Featured Collection</h2>
                    <p class="section-subtitle">Top 10 Most Sold This Week, Next Day Delivery</p>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button style="width: 40px; height: 40px; border-radius: 50%; background: var(--light-gray); border: none; cursor: pointer;">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button style="width: 40px; height: 40px; border-radius: 50%; background: var(--primary-blue); color: white; border: none; cursor: pointer;">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="product-grid">
                <!-- Product 1 -->
                <div class="product-card">
                    <div class="product-badge">Save Rs. 500.00</div>
                    <div class="product-image">
                        <div class="product-placeholder">GS</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">ShaktiPure</div>
                        <h3 class="product-title">Compact Gas Safety Device</h3>
                        <div class="product-price">
                            <span class="current-price">Rs. 1,800.00</span>
                            <span class="original-price">Rs. 2,300.00</span>
                        </div>
                        <div class="product-colors">
                            <div class="color-option" style="background: #ef4444;"></div>
                            <div class="color-option" style="background: #3b82f6;"></div>
                            <div class="color-option" style="background: #10b981;"></div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-cart">Add to Cart</button>
                            <button class="btn-quick-view">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 2 -->
                <div class="product-card">
                    <div class="product-badge">Save Rs. 200.00</div>
                    <div class="product-image">
                        <div class="product-placeholder" style="background: linear-gradient(135deg, #f97316, #ea580c);">KC</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">ShaktiPure</div>
                        <h3 class="product-title">Smart Gas Monitor & Controller</h3>
                        <div class="product-price">
                            <span class="current-price">Rs. 1,200.00</span>
                            <span class="original-price">Rs. 1,400.00</span>
                        </div>
                        <div class="product-colors">
                            <div class="color-option" style="background: #f59e0b;"></div>
                            <div class="color-option" style="background: #8b5cf6;"></div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-cart">Add to Cart</button>
                            <button class="btn-quick-view">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 3 -->
                <div class="product-card">
                    <div class="product-badge">Best Seller</div>
                    <div class="product-image">
                        <div class="product-placeholder" style="background: linear-gradient(135deg, #10b981, #059669);">OE</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">ShaktiPure</div>
                        <h3 class="product-title">Advanced Gas Leak Detector</h3>
                        <div class="product-price">
                            <span class="current-price">Rs. 4,500.00</span>
                            <span class="original-price">Rs. 4,900.00</span>
                        </div>
                        <div class="product-colors">
                            <div class="color-option" style="background: #000000;"></div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-cart">Add to Cart</button>
                            <button class="btn-quick-view">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 4 -->
                <div class="product-card">
                    <div class="product-badge">Save Rs. 1,000.00</div>
                    <div class="product-image">
                        <div class="product-placeholder" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">PO</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">ShaktiPure</div>
                        <h3 class="product-title">Portable Gas Safety Monitor</h3>
                        <div class="product-price">
                            <span class="current-price">Rs. 1,700.00</span>
                            <span class="original-price">Rs. 2,700.00</span>
                        </div>
                        <div class="product-colors">
                            <div class="color-option" style="background: #3b82f6;"></div>
                            <div class="color-option" style="background: #10b981;"></div>
                            <div class="color-option" style="background: #f59e0b;"></div>
                            <div class="color-option" style="background: #ef4444;"></div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-cart">Add to Cart</button>
                            <button class="btn-quick-view">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product 5 -->
                <div class="product-card">
                    <div class="product-badge">Save Rs. 1,000.00</div>
                    <div class="product-image">
                        <div class="product-placeholder" style="background: linear-gradient(135deg, #ef4444, #dc2626);">WD</div>
                    </div>
                    <div class="product-info">
                        <div class="product-brand">ShaktiPure</div>
                        <h3 class="product-title">Industrial Safety System</h3>
                        <div class="product-price">
                            <span class="current-price">Rs. 19,000.00</span>
                            <span class="original-price">Rs. 20,000.00</span>
                        </div>
                        <div class="product-colors">
                            <div class="color-option" style="background: #000000;"></div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-cart">Add to Cart</button>
                            <button class="btn-quick-view">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Deal of the Days -->
    <section class="deal">
        <div class="deal-container">
            <div class="deal-header">
                <h2 class="deal-title">Deal Of The Days</h2>
                <p class="deal-subtitle">Deal Of The Day, Unbelievable Savings Await!</p>
            </div>

            <div class="countdown">
                <div class="countdown-item">
                    <span class="countdown-number" id="days">73</span>
                    <span class="countdown-label">Days</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="hours">0</span>
                    <span class="countdown-label">Hours</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="minutes">43</span>
                    <span class="countdown-label">Min</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="seconds">28</span>
                    <span class="countdown-label">Sec</span>
                </div>
            </div>

            <div class="deal-products">
                <div class="deal-product">
                    <div class="deal-product-image">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="product-brand" style="color: rgba(255,255,255,0.7);">ShaktiPure</div>
                    <h3 style="color: white; margin: 8px 0;">Compact Gas Safety Device</h3>
                    <div class="product-price" style="justify-content: center; color: white;">
                        <span class="current-price">Rs. 1,800.00</span>
                        <span class="original-price" style="color: rgba(255,255,255,0.6);">Rs. 2,300.00</span>
                    </div>
                </div>

                <div class="deal-product">
                    <div class="deal-product-image" style="background: var(--accent-orange);">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="product-brand" style="color: rgba(255,255,255,0.7);">ShaktiPure</div>
                    <h3 style="color: white; margin: 8px 0;">Smart Control System</h3>
                    <div class="product-price" style="justify-content: center; color: white;">
                        <span class="current-price">Rs. 1,200.00</span>
                        <span class="original-price" style="color: rgba(255,255,255,0.6);">Rs. 1,400.00</span>
                    </div>
                </div>

                <div class="deal-product">
                    <div class="deal-product-image" style="background: #10b981;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="product-brand" style="color: rgba(255,255,255,0.7);">ShaktiPure</div>
                    <h3 style="color: white; margin: 8px 0;">Gas Leak Alert System</h3>
                    <div class="product-price" style="justify-content: center; color: white;">
                        <span class="current-price">Rs. 4,500.00</span>
                        <span class="original-price" style="color: rgba(255,255,255,0.6);">Rs. 4,900.00</span>
                    </div>
                </div>

                <div class="deal-product">
                    <div class="deal-product-image" style="background: #8b5cf6;">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="product-brand" style="color: rgba(255,255,255,0.7);">ShaktiPure</div>
                    <h3 style="color: white; margin: 8px 0;">Professional Monitor Hub</h3>
                    <div class="product-price" style="justify-content: center; color: white;">
                        <span class="current-price">Rs. 53,300.00</span>
                        <span class="original-price" style="color: rgba(255,255,255,0.6);">Rs. 57,400.00</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Category Banners -->
    <section class="category-banners">
        <div class="section-container">
            <div class="banner-grid">
                <div class="category-banner" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));">
                    <h3>Gas Safety Solutions</h3>
                    <p>Next-Gen Technology</p>
                    <a href="#" class="btn btn-secondary">Shop Now</a>
                </div>

                <div class="category-banner" style="background: linear-gradient(135deg, #059669, #10b981);">
                    <h3>Smart Detection</h3>
                    <p>Sound Meets Innovation</p>
                    <a href="#" class="btn btn-secondary">Shop Now</a>
                </div>

                <div class="category-banner" style="background: linear-gradient(135deg, var(--dark-blue), var(--primary-blue));">
                    <h3>Industrial Solutions</h3>
                    <p>Performance On-The-Go</p>
                    <a href="#" class="btn btn-secondary">Shop Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-grid">
                <div class="footer-section">
                    <h4>MY ACCOUNT</h4>
                    <ul>
                        <li><a href="#">My Account</a></li>
                        <li><a href="#">Register</a></li>
                        <li><a href="#">Login</a></li>
                        <li><a href="#">Franchisee/Store Login</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>CUSTOMER SERVICE</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms & Conditions</a></li>
                        <li><a href="#">Shipping & Returns</a></li>
                        <li><a href="#">Support Center</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>QUICK LINKS</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Products</a></li>
                        <li><a href="#">Business Opportunity</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Legal Documents</a></li>
                        <li><a href="#">Awards</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>CONTACT US</h4>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-map-marker-alt" style="color: var(--accent-orange); margin-right: 10px;"></i>
                        <span style="color: rgba(255,255,255,0.8); font-size: 14px; line-height: 1.6;">
                            Shakti Pure Industries Pvt Ltd<br>
                            D-224, Udhana Complex, Udhana<br>
                            Surat, 394210, Gujarat, India
                        </span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-envelope" style="color: var(--accent-orange); margin-right: 10px;"></i>
                        <span style="color: rgba(255,255,255,0.8); font-size: 14px;"><EMAIL></span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-phone" style="color: var(--accent-orange); margin-right: 10px;"></i>
                        <span style="color: rgba(255,255,255,0.8); font-size: 14px;">+91 8460403679</span>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Shakti Pure Industries Pvt Ltd. All Rights Reserved.</p>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Menu Toggle
        document.querySelector('.mobile-menu-btn').addEventListener('click', function() {
            document.getElementById('mobileMenu').classList.add('active');
        });

        function closeMobileMenu() {
            document.getElementById('mobileMenu').classList.remove('active');
        }

        // Hero Slider
        let currentSlide = 0;
        const slides = [
            {
                title: "Stay Ahead With Latest Gas Safety Solutions",
                subtitle: "Discover the newest safety devices and innovative systems that keep you ahead."
            },
            {
                title: "Advanced Smart Detection Technology",
                subtitle: "Next-generation monitoring systems with real-time alerts and mobile integration."
            },
            {
                title: "Industrial Grade Safety Equipment",
                subtitle: "Professional solutions for commercial and industrial applications."
            }
        ];

        function updateSlide() {
            const heroContent = document.querySelector('.hero-content h1');
            const heroSubtitle = document.querySelector('.hero-subtitle');
            const dots = document.querySelectorAll('.nav-dot');
            
            heroContent.textContent = slides[currentSlide].title;
            heroSubtitle.textContent = slides[currentSlide].subtitle;
            
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        }

        // Auto slide
        setInterval(() => {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlide();
        }, 5000);

        // Dot navigation
        document.querySelectorAll('.nav-dot').forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlide();
            });
        });

        // Countdown Timer
        function updateCountdown() {
            const now = new Date().getTime();
            const targetDate = new Date().getTime() + (73 * 24 * 60 * 60 * 1000); // 73 days from now
            const distance = targetDate - now;

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('days').textContent = days;
            document.getElementById('hours').textContent = hours;
            document.getElementById('minutes').textContent = minutes;
            document.getElementById('seconds').textContent = seconds;
        }

        setInterval(updateCountdown, 1000);
        updateCountdown();

        // Scroll Animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Add scroll animation class to elements
        document.querySelectorAll('.collection-card, .product-card, .category-banner').forEach(el => {
            el.classList.add('scroll-animate');
            observer.observe(el);
        });

        // Product interactions
        document.querySelectorAll('.btn-add-cart').forEach(btn => {
            btn.addEventListener('click', function() {
                const productTitle = this.closest('.product-card').querySelector('.product-title').textContent;
                alert(`Added "${productTitle}" to cart!`);
                
                // Update cart badge
                const cartBadge = document.querySelector('.cart-badge');
                const currentCount = parseInt(cartBadge.textContent);
                cartBadge.textContent = currentCount + 1;
            });
        });

        // Search functionality
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm.trim()) {
                alert(`Searching for: ${searchTerm}`);
            }
        });

        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ShaktiPure Modern Website Loaded Successfully!');
            
            // Add fade-in animation to hero content
            document.querySelector('.hero-content').classList.add('fade-in-up');
            
            // Stagger animation for collection cards
            setTimeout(() => {
                document.querySelectorAll('.collection-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 500);
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    </script>
</body>
</html>
