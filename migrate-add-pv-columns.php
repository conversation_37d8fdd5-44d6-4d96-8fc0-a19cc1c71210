<?php
/**
 * Database Migration: Add PV Columns to Users Table
 * MLM Binary Plan System
 * 
 * This script adds the missing self_pv and upline_pv columns to the users table
 * to fix the "Column not found: 1054 Unknown column 'self_pv'" error
 */

require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>Database Migration - Add PV Columns</title></head><body>\n";
    echo "<h1>🔧 Database Migration: Adding PV Columns</h1>\n";
    
    // Check if columns already exist
    echo "<h2>1. Checking Current Table Structure</h2>\n";
    
    $checkStmt = $pdo->query("DESCRIBE users");
    $columns = $checkStmt->fetchAll(PDO::FETCH_COLUMN);
    
    $selfPvExists = in_array('self_pv', $columns);
    $uplinePvExists = in_array('upline_pv', $columns);
    
    echo "<p>Current columns in users table: " . implode(', ', $columns) . "</p>\n";
    echo "<p>self_pv column exists: " . ($selfPvExists ? '✅ Yes' : '❌ No') . "</p>\n";
    echo "<p>upline_pv column exists: " . ($uplinePvExists ? '✅ Yes' : '❌ No') . "</p>\n";
    
    // Add missing columns
    echo "<h2>2. Adding Missing Columns</h2>\n";
    
    $alterQueries = [];
    $messages = [];
    
    if (!$selfPvExists) {
        $alterQueries[] = "ALTER TABLE users ADD COLUMN self_pv DECIMAL(10,2) DEFAULT 0.00 AFTER placement_side";
        $messages[] = "Adding self_pv column";
    } else {
        $messages[] = "self_pv column already exists - skipping";
    }
    
    if (!$uplinePvExists) {
        $alterQueries[] = "ALTER TABLE users ADD COLUMN upline_pv DECIMAL(10,2) DEFAULT 0.00 AFTER self_pv";
        $messages[] = "Adding upline_pv column";
    } else {
        $messages[] = "upline_pv column already exists - skipping";
    }
    
    // Execute ALTER queries
    foreach ($alterQueries as $index => $query) {
        try {
            echo "<p>🔄 " . $messages[$index] . "...</p>\n";
            $pdo->exec($query);
            echo "<p style='color: green;'>✅ " . $messages[$index] . " - SUCCESS</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ " . $messages[$index] . " - FAILED: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    }
    
    // Display remaining messages for existing columns
    if ($selfPvExists && $uplinePvExists) {
        echo "<p style='color: orange;'>⚠️ Both columns already exist - no migration needed</p>\n";
    }
    
    // Verify the changes
    echo "<h2>3. Verifying Changes</h2>\n";
    
    $verifyStmt = $pdo->query("DESCRIBE users");
    $newColumns = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr style='background: #f8f9fa;'><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr>\n";
    
    foreach ($newColumns as $column) {
        $highlight = '';
        if ($column['Field'] === 'self_pv' || $column['Field'] === 'upline_pv') {
            $highlight = ' style="background: #d4edda;"';
        }
        echo "<tr{$highlight}>\n";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test the PV system
    echo "<h2>4. Testing PV System</h2>\n";
    
    try {
        // Test if we can now query the PV columns
        $testStmt = $pdo->prepare("SELECT self_pv, upline_pv FROM users LIMIT 1");
        $testStmt->execute();
        echo "<p style='color: green;'>✅ PV columns are now accessible - migration successful!</p>\n";
        
        // Update existing users with default PV values if needed
        $updateStmt = $pdo->prepare("UPDATE users SET self_pv = 0.00, upline_pv = 0.00 WHERE self_pv IS NULL OR upline_pv IS NULL");
        $updateStmt->execute();
        $updatedRows = $updateStmt->rowCount();
        
        if ($updatedRows > 0) {
            echo "<p style='color: blue;'>ℹ️ Updated {$updatedRows} existing users with default PV values (0.00)</p>\n";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ PV system test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    echo "<h2>5. Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ Migration Completed Successfully!</h3>\n";
    echo "<p>The following changes have been made to fix the PV system error:</p>\n";
    echo "<ul>\n";
    echo "<li>Added <code>self_pv</code> column to users table (DECIMAL(10,2) DEFAULT 0.00)</li>\n";
    echo "<li>Added <code>upline_pv</code> column to users table (DECIMAL(10,2) DEFAULT 0.00)</li>\n";
    echo "<li>Set default values for existing users</li>\n";
    echo "<li>Verified PV system functionality</li>\n";
    echo "</ul>\n";
    echo "<p><strong>The error 'Unknown column self_pv' should now be resolved!</strong></p>\n";
    echo "</div>\n";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<strong>📝 Next Steps:</strong><br>\n";
    echo "• Test your user dashboard to confirm the error is fixed<br>\n";
    echo "• The PV system should now work correctly<br>\n";
    echo "• Consider updating setup.php to include these columns for future installations\n";
    echo "</div>\n";
    
    echo "</body></html>\n";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
