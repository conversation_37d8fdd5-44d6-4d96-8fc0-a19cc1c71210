<?php
/**
 * Database Migration: Fix Products Table
 * MLM Binary Plan System
 * 
 * This script fixes the products table by adding the missing image column
 * to resolve product addition errors
 */

require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>Database Migration - Fix Products Table</title></head><body>\n";
    echo "<h1>🔧 Database Migration: Fixing Products Table</h1>\n";
    
    // Check current table structure
    echo "<h2>1. Checking Current Products Table Structure</h2>\n";
    
    $checkStmt = $pdo->query("DESCRIBE products");
    $columns = $checkStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = array_column($columns, 'Field');
    $imageExists = in_array('image', $columnNames);
    
    echo "<p>Current columns in products table:</p>\n";
    echo "<ul>\n";
    foreach ($columnNames as $column) {
        $highlight = ($column === 'image') ? ' style="color: green; font-weight: bold;"' : '';
        echo "<li{$highlight}>{$column}</li>\n";
    }
    echo "</ul>\n";
    
    echo "<p>Image column exists: " . ($imageExists ? '✅ Yes' : '❌ No') . "</p>\n";
    
    // Add missing image column if needed
    echo "<h2>2. Adding Missing Image Column</h2>\n";
    
    if (!$imageExists) {
        try {
            echo "<p>🔄 Adding image column to products table...</p>\n";
            $alterQuery = "ALTER TABLE products ADD COLUMN image VARCHAR(255) NULL AFTER description";
            $pdo->exec($alterQuery);
            echo "<p style='color: green;'>✅ Image column added successfully!</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Failed to add image column: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Image column already exists - no migration needed</p>\n";
    }
    
    // Verify the changes
    echo "<h2>3. Verifying Changes</h2>\n";
    
    $verifyStmt = $pdo->query("DESCRIBE products");
    $newColumns = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #f8f9fa;'><th>Column</th><th>Type</th><th>Null</th><th>Default</th><th>Extra</th></tr>\n";
    
    foreach ($newColumns as $column) {
        $highlight = '';
        if ($column['Field'] === 'image') {
            $highlight = ' style="background: #d4edda;"';
        }
        echo "<tr{$highlight}>\n";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>\n";
        echo "<td>" . htmlspecialchars($column['Extra'] ?? '') . "</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test product insertion
    echo "<h2>4. Testing Product Creation</h2>\n";
    
    try {
        // Test if we can now insert a product with image column
        $testStmt = $pdo->prepare("INSERT INTO products (product_code, name, description, image, price, pv_value, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        // Use a test product that we'll delete immediately
        $testProductCode = 'TEST_' . uniqid();
        $testStmt->execute([
            $testProductCode,
            'Test Product',
            'Test Description',
            null, // No image for test
            99.99,
            10.00,
            'active',
            1 // Assuming admin ID 1 exists
        ]);
        
        // Delete the test product
        $deleteStmt = $pdo->prepare("DELETE FROM products WHERE product_code = ?");
        $deleteStmt->execute([$testProductCode]);
        
        echo "<p style='color: green;'>✅ Product creation test successful - the error should now be fixed!</p>\n";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Product creation test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p>This might indicate other issues that need to be resolved.</p>\n";
    }
    
    // Check for other potential issues
    echo "<h2>5. Additional Checks</h2>\n";
    
    // Check if admin table exists and has records
    try {
        $adminCheckStmt = $pdo->query("SELECT COUNT(*) as count FROM admin");
        $adminCount = $adminCheckStmt->fetch()['count'];
        
        if ($adminCount > 0) {
            echo "<p style='color: green;'>✅ Admin table has {$adminCount} record(s)</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ Admin table is empty - you may need to create an admin user first</p>\n";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Admin table check failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check uploads directory
    $uploadsDir = 'uploads/products/';
    if (!is_dir($uploadsDir)) {
        echo "<p style='color: orange;'>⚠️ Products upload directory doesn't exist: {$uploadsDir}</p>\n";
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p style='color: green;'>✅ Created products upload directory</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to create products upload directory</p>\n";
        }
    } else {
        echo "<p style='color: green;'>✅ Products upload directory exists</p>\n";
    }
    
    echo "<h2>6. Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ Migration Completed Successfully!</h3>\n";
    echo "<p>The following changes have been made to fix product addition errors:</p>\n";
    echo "<ul>\n";
    echo "<li>Added <code>image</code> column to products table (VARCHAR(255) NULL)</li>\n";
    echo "<li>Verified product creation functionality</li>\n";
    echo "<li>Checked admin table and upload directories</li>\n";
    echo "</ul>\n";
    echo "<p><strong>You should now be able to add products without errors!</strong></p>\n";
    echo "</div>\n";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<strong>📝 Next Steps:</strong><br>\n";
    echo "• Test adding a product through the admin panel<br>\n";
    echo "• Ensure you have admin user credentials to access the admin panel<br>\n";
    echo "• Check that file uploads work correctly for product images\n";
    echo "</div>\n";
    
    echo "</body></html>\n";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
