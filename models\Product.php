<?php
/**
 * Product Model
 * MLM Binary Plan System
 */

require_once 'BaseModel.php';

class Product extends BaseModel {
    protected $table = 'products';
    protected $primaryKey = 'id';
    protected $fillable = [
        'product_code', 'name', 'description', 'image', 'price', 'pv_value',
        'status', 'created_by'
    ];

    /**
     * Find product by product code
     *
     * @param string $productCode Product code
     * @return array|false Product data or false if not found
     */
    public function findByProductCode($productCode) {
        return $this->findBy('product_code', $productCode);
    }

    /**
     * Get active products
     *
     * @param int $limit Maximum number of products to return
     * @param int $offset Offset for pagination
     * @return array Active products
     */
    public function getActiveProducts($limit = 1000, $offset = 0) {
        return $this->getBy('status', 'active', $limit, $offset, 'name', 'ASC');
    }

    /**
     * Search products
     *
     * @param string $query Search query
     * @param int $limit Maximum number of products to return
     * @param int $offset Offset for pagination
     * @return array Search results
     */
    public function searchProducts($query, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT * FROM {$this->table}
            WHERE (name LIKE ? OR description LIKE ? OR product_code LIKE ?)
            AND status = 'active'
            ORDER BY name ASC
            LIMIT ? OFFSET ?
        ");

        $searchTerm = "%{$query}%";
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * Get products by price range
     *
     * @param float $minPrice Minimum price
     * @param float $maxPrice Maximum price
     * @param int $limit Maximum number of products to return
     * @param int $offset Offset for pagination
     * @return array Products in price range
     */
    public function getProductsByPriceRange($minPrice, $maxPrice, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT * FROM {$this->table}
            WHERE price BETWEEN ? AND ?
            AND status = 'active'
            ORDER BY price ASC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$minPrice, $maxPrice, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * Get products by PV range
     *
     * @param float $minPV Minimum PV value
     * @param float $maxPV Maximum PV value
     * @param int $limit Maximum number of products to return
     * @param int $offset Offset for pagination
     * @return array Products in PV range
     */
    public function getProductsByPVRange($minPV, $maxPV, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT * FROM {$this->table}
            WHERE pv_value BETWEEN ? AND ?
            AND status = 'active'
            ORDER BY pv_value ASC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$minPV, $maxPV, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * Create a new product
     *
     * @param array $data Product data
     * @return int|false Product ID or false on failure
     */
    public function createProduct(array $data) {
        // Generate product code if not provided
        if (empty($data['product_code'])) {
            $data['product_code'] = $this->generateProductCode();
        }

        // Set default status if not provided
        if (empty($data['status'])) {
            $data['status'] = 'active';
        }

        return $this->create($data);
    }

    /**
     * Generate a unique product code
     *
     * @return string Product code
     */
    private function generateProductCode() {
        $prefix = 'PRD';
        $timestamp = time();
        $random = mt_rand(100, 999);

        return $prefix . $timestamp . $random;
    }

    /**
     * Get product statistics
     *
     * @return array Product statistics
     */
    public function getProductStats() {
        $stats = [];

        // Total products
        $stats['total_products'] = $this->count();

        // Active products
        $stats['active_products'] = $this->count('status', 'active');

        // Inactive products
        $stats['inactive_products'] = $this->count('status', 'inactive');

        // Average price
        $stmt = $this->db->query("SELECT AVG(price) as avg_price FROM {$this->table} WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['average_price'] = (float) ($result['avg_price'] ?? 0);

        // Average PV value
        $stmt = $this->db->query("SELECT AVG(pv_value) as avg_pv FROM {$this->table} WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['average_pv'] = (float) ($result['avg_pv'] ?? 0);

        // Price range
        $stmt = $this->db->query("SELECT MIN(price) as min_price, MAX(price) as max_price FROM {$this->table} WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['min_price'] = (float) ($result['min_price'] ?? 0);
        $stats['max_price'] = (float) ($result['max_price'] ?? 0);

        // PV range
        $stmt = $this->db->query("SELECT MIN(pv_value) as min_pv, MAX(pv_value) as max_pv FROM {$this->table} WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['min_pv'] = (float) ($result['min_pv'] ?? 0);
        $stats['max_pv'] = (float) ($result['max_pv'] ?? 0);

        return $stats;
    }

    /**
     * Get top selling products
     *
     * @param int $limit Maximum number of products to return
     * @param int $days Number of days to consider
     * @return array Top selling products
     */
    public function getTopSellingProducts($limit = 10, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT p.*, COUNT(po.id) as order_count, SUM(po.quantity) as total_quantity
            FROM {$this->table} p
            LEFT JOIN purchase_orders po ON p.id = po.product_id
            WHERE po.created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            AND po.order_status = 'confirmed'
            AND p.status = 'active'
            GROUP BY p.id
            ORDER BY order_count DESC, total_quantity DESC
            LIMIT ?
        ");
        $stmt->execute([$days, $limit]);
        return $stmt->fetchAll();
    }

    /**
     * Get product sales data
     *
     * @param int $productId Product ID
     * @param int $days Number of days to get data for
     * @return array Product sales data
     */
    public function getProductSalesData($productId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(created_at) as date, COUNT(*) as order_count, SUM(quantity) as total_quantity, SUM(total_amount) as total_amount
            FROM purchase_orders
            WHERE product_id = ?
            AND order_status = 'confirmed'
            AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$productId, $days]);
        return $stmt->fetchAll();
    }

    /**
     * Update product image
     *
     * @param int $productId Product ID
     * @param string $imageUrl Image URL
     * @return bool Success or failure
     */
    public function updateProductImage($productId, $imageUrl) {
        $stmt = $this->db->prepare("UPDATE {$this->table} SET image_url = ?, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$imageUrl, $productId]);
    }

    /**
     * Get products with low stock (if stock management is implemented)
     *
     * @param int $threshold Stock threshold
     * @param int $limit Maximum number of products to return
     * @return array Products with low stock
     */
    public function getLowStockProducts($threshold = 10, $limit = 50) {
        // This would be implemented if stock management is added to the system
        // For now, return empty array
        return [];
    }
}
?>
