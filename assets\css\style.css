/**
 * Main Stylesheet
 * MLM Binary Plan System
 */

/* Custom Variables */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-1px);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Dashboard Styles */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
}

.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stats-icon.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stats-icon.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stats-icon.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

/* Binary Tree Styles */
.tree-container {
    overflow-x: auto;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.tree-node {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem;
    text-align: center;
    min-width: 120px;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.tree-node:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.tree-node.active-user {
    border-color: var(--success-color);
    background: #f8fff9;
}

.tree-node.inactive-user {
    border-color: var(--danger-color);
    background: #fff5f5;
}

.tree-line {
    border-left: 2px solid #dee2e6;
    height: 30px;
    margin: 0 auto;
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Wallet Styles */
.wallet-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
}

.wallet-balance {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* PV Display */
.pv-display {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.pv-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

/* Responsive */
@media (max-width: 768px) {
    .tree-container {
        padding: 1rem;
    }

    .tree-node {
        min-width: 100px;
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .wallet-balance {
        font-size: 2rem;
    }

    .pv-value {
        font-size: 1.5rem;
    }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: var(--box-shadow);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* Product Management Responsive Styles */
.table-responsive {
    border-radius: var(--border-radius);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table th {
    border-top: none;
    font-weight: 600;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

.product-image {
    transition: transform 0.2s ease-in-out;
}

.product-image:hover {
    transform: scale(1.1);
}

/* Mobile Responsive Adjustments */
@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group-vertical .btn {
        margin-bottom: 2px;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-lg {
        max-width: calc(100vw - 1rem);
    }
}

@media (max-width: 575.98px) {
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .table {
        font-size: 0.8rem;
    }

    .badge {
        font-size: 0.7rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h4 {
        font-size: 1.1rem;
    }

    h5 {
        font-size: 1rem;
    }
}

/* Image Modal Styles */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

#imageModalImg {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
}

/* Enhanced Button Styles */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

.btn-group-vertical .btn {
    border-radius: var(--border-radius);
    margin-bottom: 2px;
}

/* Loading Animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
