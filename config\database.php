<?php
/**
 * Database Configuration
 * MLM Binary Plan System
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'u404256496_shaktipure');
define('DB_USER', 'u404256496_shaktipure');
define('DB_PASS', 'Shaktipure@7041');

// Environment Configuration
define('ENVIRONMENT', 'development'); // development or production

// Razorpay Configuration
define('RAZORPAY_KEY_ID', 'rzp_test_VlmnmcVPhKby27');
define('RAZORPAY_KEY_SECRET', 'L4XLmdPsKd6alBe315rbXsme');
define('RAZORPAY_MODE', 'test'); // test or live

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'kindsgqqxbpqknlk');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'ShaktiPure MLM');

// Application Configuration
define('SITE_URL', 'http://localhost/shaktipure');
define('SITE_NAME', 'ShaktiPure MLM');
define('ADMIN_EMAIL', '<EMAIL>');

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('PASSWORD_MIN_LENGTH', 6);

// MLM Configuration
define('PV_RATE', 0.10); // 1 PV = ₹0.10
define('DAILY_CAPPING', 130000); // ₹130,000 per day
define('MIN_WITHDRAWAL', 500); // Minimum ₹500 withdrawal

// File Upload Configuration
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Error Reporting
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
