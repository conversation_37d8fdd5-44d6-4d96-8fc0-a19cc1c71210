<?php
/**
 * Create Admin, <PERSON><PERSON><PERSON><PERSON>, and Master Users WITHOUT Password Hashing
 * MLM Binary Plan System
 *
 * ⚠️ SECURITY WARNING ⚠️
 * This script creates users with plain text passwords.
 * This is for development/testing purposes only.
 * Never use plain text passwords in production!
 */

require_once 'config/Connection.php';
require_once 'includes/BinaryTree.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>Create Users - No Password Hashing</title></head><body>\n";
    echo "<h1>🔓 Creating Users WITHOUT Password Hashing</h1>\n";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<strong>⚠️ SECURITY WARNING:</strong> This script creates users with plain text passwords for development/testing only!\n";
    echo "</div>\n";

    $results = [];

    // 1. Create Admin User
    echo "<h2>1. Creating Admin User</h2>\n";

    // Check if admin user already exists
    $checkAdmin = $pdo->prepare("SELECT id FROM admin WHERE username = ? OR email = ?");
    $checkAdmin->execute(['admin', '<EMAIL>']);

    if ($checkAdmin->fetch()) {
        echo "<p style='color: orange;'>⚠️ Admin user already exists!</p>\n";
        $results['admin'] = 'exists';
    } else {
        // Create admin user with plain text password
        $adminStmt = $pdo->prepare("INSERT INTO admin (username, email, password, full_name, phone, status) VALUES (?, ?, ?, ?, ?, ?)");
        $adminStmt->execute([
            'admin',
            '<EMAIL>',
            'admin123', // Plain text password - NO HASHING
            'System Administrator',
            '+91-9999999999',
            'active'
        ]);

        echo "<p style='color: green;'>✅ Admin User created successfully!</p>\n";
        echo "<ul>\n";
        echo "<li><strong>Username:</strong> admin</li>\n";
        echo "<li><strong>Password:</strong> admin123</li>\n";
        echo "<li><strong>Email:</strong> <EMAIL></li>\n";
        echo "<li><strong>Login URL:</strong> <a href='admin/login.php'>admin/login.php</a></li>\n";
        echo "</ul>\n";
        $results['admin'] = 'created';
    }

    // 2. Create Franchise User
    echo "<h2>2. Creating Franchise User</h2>\n";

    // Check if franchise user already exists
    $checkFranchise = $pdo->prepare("SELECT id FROM franchise WHERE username = ? OR email = ?");
    $checkFranchise->execute(['franchise', '<EMAIL>']);

    if ($checkFranchise->fetch()) {
        echo "<p style='color: orange;'>⚠️ Franchise user already exists!</p>\n";
        $results['franchise'] = 'exists';
    } else {
        // Generate unique franchise code
        $franchiseCode = 'FR' . str_pad(1, 4, '0', STR_PAD_LEFT); // FR0001

        // Get admin ID for created_by field
        $adminStmt = $pdo->prepare("SELECT id FROM admin WHERE username = 'admin' LIMIT 1");
        $adminStmt->execute();
        $admin = $adminStmt->fetch();
        $adminId = $admin ? $admin['id'] : 1;

        // Create franchise user with plain text password
        $franchiseStmt = $pdo->prepare("INSERT INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $franchiseStmt->execute([
            $franchiseCode,
            'franchise',
            '<EMAIL>',
            'franchise123', // Plain text password - NO HASHING
            'Master Franchise',
            '+91-8888888888',
            'Franchise Address',
            5.00, // 5% commission rate
            'active',
            $adminId
        ]);

        echo "<p style='color: green;'>✅ Franchise User created successfully!</p>\n";
        echo "<ul>\n";
        echo "<li><strong>Franchise Code:</strong> {$franchiseCode}</li>\n";
        echo "<li><strong>Username:</strong> franchise</li>\n";
        echo "<li><strong>Password:</strong> franchise123</li>\n";
        echo "<li><strong>Email:</strong> <EMAIL></li>\n";
        echo "<li><strong>Login URL:</strong> <a href='franchise/login.php'>franchise/login.php</a></li>\n";
        echo "</ul>\n";
        $results['franchise'] = 'created';
    }

    // 3. Create Master User
    echo "<h2>3. Creating Master User</h2>\n";

    // Check if master user already exists
    $checkUser = $pdo->prepare("SELECT user_id FROM users WHERE username = ? OR email = ?");
    $checkUser->execute(['master', '<EMAIL>']);

    if ($checkUser->fetch()) {
        echo "<p style='color: orange;'>⚠️ Master user already exists!</p>\n";
        $results['master'] = 'exists';
    } else {
        // Generate unique user ID
        $masterUserId = 'SP' . str_pad(1, 6, '0', STR_PAD_LEFT); // SP000001

        // Create master user with plain text password
        $userStmt = $pdo->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, self_pv, upline_pv, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $userStmt->execute([
            $masterUserId,
            'master',
            '<EMAIL>',
            'master123', // Plain text password - NO HASHING
            'Master User',
            '+91-9999999999',
            'Master Address',
            null, // No sponsor for master user
            null, // No franchise for master user
            null, // No placement side for master user
            0.00, // Initial self_pv
            0.00, // Initial upline_pv
            'active'
        ]);

        // Create wallet for master user
        $walletStmt = $pdo->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, ?, ?, ?)");
        $walletStmt->execute([$masterUserId, 0.00, 0.00, 0.00]);

        // Add to binary tree as root
        $binaryTree = new BinaryTree();
        $treeResult = $binaryTree->addUser($masterUserId, null, 'root', false);

        echo "<p style='color: green;'>✅ Master User created successfully!</p>\n";
        echo "<ul>\n";
        echo "<li><strong>User ID:</strong> {$masterUserId}</li>\n";
        echo "<li><strong>Username:</strong> master</li>\n";
        echo "<li><strong>Password:</strong> master123</li>\n";
        echo "<li><strong>Email:</strong> <EMAIL></li>\n";
        echo "<li><strong>Login URL:</strong> <a href='user/login.php'>user/login.php</a></li>\n";
        echo "</ul>\n";
        $results['master'] = 'created';
    }

    // Summary
    echo "<hr>\n";
    echo "<h2>📋 Summary</h2>\n";

    $createdCount = count(array_filter($results, function($status) { return $status === 'created'; }));
    $existsCount = count(array_filter($results, function($status) { return $status === 'exists'; }));

    if ($createdCount > 0) {
        echo "<p style='color: green;'>✅ {$createdCount} user(s) created successfully!</p>\n";
    }
    if ($existsCount > 0) {
        echo "<p style='color: orange;'>⚠️ {$existsCount} user(s) already existed.</p>\n";
    }

    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>🔑 Login Credentials (Plain Text Passwords)</h3>\n";
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th>User Type</th><th>Login URL</th><th>Username</th><th>Password</th><th>Email</th>\n";
    echo "</tr>\n";
    echo "<tr>\n";
    echo "<td><strong>Admin</strong></td>\n";
    echo "<td><a href='admin/login.php'>admin/login.php</a></td>\n";
    echo "<td>admin</td>\n";
    echo "<td>admin123</td>\n";
    echo "<td><EMAIL></td>\n";
    echo "</tr>\n";
    echo "<tr>\n";
    echo "<td><strong>Franchise</strong></td>\n";
    echo "<td><a href='franchise/login.php'>franchise/login.php</a></td>\n";
    echo "<td>franchise</td>\n";
    echo "<td>franchise123</td>\n";
    echo "<td><EMAIL></td>\n";
    echo "</tr>\n";
    echo "<tr>\n";
    echo "<td><strong>Master User</strong></td>\n";
    echo "<td><a href='user/login.php'>user/login.php</a></td>\n";
    echo "<td>master</td>\n";
    echo "<td>master123</td>\n";
    echo "<td><EMAIL></td>\n";
    echo "</tr>\n";
    echo "</table>\n";
    echo "</div>\n";

    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<strong>🔒 Security Reminder:</strong><br>\n";
    echo "• These passwords are stored in plain text (no hashing)<br>\n";
    echo "• This is for development/testing purposes only<br>\n";
    echo "• Change passwords after first login<br>\n";
    echo "• Never use plain text passwords in production!\n";
    echo "</div>\n";

    echo "</body></html>\n";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
